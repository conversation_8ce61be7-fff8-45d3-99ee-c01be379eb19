#if USE_NUNU_SDK && BBB_DEBUG
using System.Collections.Generic;
using System.Linq;
using BBB;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Wallet;
using BebopBee;
using FBConfig;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Promotions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Nunu.Flayer;
using Nunu.Network.Logging;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Scripting;

namespace GameAssets.Scripts.Nunu.NunuFlayerFunctions
{
    public static class GameState
    {
        private static GameController _gameController;
        private static IAccountManager _accountManager;
        private static IWalletManager _walletManager;
        private static ILivesManager _livesManager;
        private static IPlayerManager _playerManager;
        private static ILocationManager _locationManager;
        private static PromotionManager _promotionManager;
        private static IConfig _config;
        private static IEventDispatcher _eventDispatcher;
        private static IEpisodeTaskManager _episodeTaskManager;
        private static IEpisodicScenesManager _episodicScenesManager;

        public static void InitGameContext(IContext context)
        {
            _accountManager = context.Resolve<IAccountManager>();
            _walletManager = context.Resolve<IWalletManager>();
            _livesManager = context.Resolve<ILivesManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            _locationManager = context.Resolve<ILocationManager>();
            _promotionManager = context.Resolve<PromotionManager>();
            _config = context.Resolve<IConfig>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _episodeTaskManager = context.Resolve<IEpisodeTaskManager>();
            _episodicScenesManager = context.Resolve<IEpisodicScenesManager>();
        }

        public static void InitLevelContext(IContext context)
        {
            _gameController = context.Resolve<GameController>();
        }

        [FlayerFunction("game_state", "returns the actual game state")]
        [Preserve]
        public static string HandleGameState(PacketLogger packetLogger)
        {
            var gameState = CreateBaseGameState();

            if (IsInActiveGame())
            {
                AddBoardState(gameState);
                AddLevelData(gameState);
            }
            AddPlayerData(gameState);

            return JsonConvert.SerializeObject(gameState);
        }

        private static JObject CreateBaseGameState()
        {
            return new JObject
            {
                ["_can_interact"] = true,
                ["_hint_key"] = SceneManager.GetActiveScene().name
            };
        }

        private static bool IsInActiveGame()
        {
            return _gameController != null && _gameController.Level is { Grid: not null };
        }

        private static void AddBoardState(JObject gameState)
        {
            gameState["board"] = new JObject
            {
                ["width"] = _gameController.Level.Grid.Width,
                ["height"] = _gameController.Level.Grid.Height,
                //Might need to add in the future, pls don't delete
                //["board"] = JArray.FromObject(_gameController.Level.Grid.Cells),
                ["valid_moves"] = JArray.FromObject(GetPossibleMoves())
            };
        }

        private static List<LightPossibleMove> GetPossibleMoves()
        {
            var result = new List<LightPossibleMove>();

            var possibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(_gameController.Level.Grid);

            foreach (var possibleMove in possibleMoves)
            {
                result.Add(new LightPossibleMove()
                {
                    FirstCellCoords = new LightCoords()
                    {
                        X = possibleMove.FirstCell.Coords.X,
                        Y = possibleMove.FirstCell.Coords.Y
                    },
                    SecondCellCoords = new LightCoords()
                    {
                        X = possibleMove.SecondCell.Coords.X,
                        Y = possibleMove.SecondCell.Coords.Y
                    }
                });
            }
            return result;
        }

        private static void AddLevelData(JObject gameState)
        {
            gameState["level_data"] = new JObject
            {
                ["any_goals_left"] = _gameController?.Level?.Goals?.AnyGoalsLeft() ?? false,
                ["goals"] = _gameController?.Level?.Goals?.ToString() ?? string.Empty,
                ["level_name"] = _gameController?.Level?.LevelName ?? string.Empty,
                ["rewards"] = _gameController?.Level?.RewardsDictionary != null ? JObject.FromObject(_gameController.Level.RewardsDictionary) : new JObject(),
                ["remaining_moves"] = _gameController?.RemainingMoves ?? 0,
                ["difficulty"] = _gameController?.Level?.Stage ?? 0
            };
        }

        private static void AddPlayerData(JObject gameState)
        {
            gameState["player_data"] = new JObject
            {
                ["stars"] = _playerManager?.StarCount ?? 0,
                ["lives"] = _livesManager?.NumberOfLives ?? 0,
                ["trophies"] = _accountManager?.Profile?.Trophies ?? 0,
                ["wallet"] = _walletManager?.Balance?.GetCurrenciesDelta() != null ? JObject.FromObject(_walletManager.Balance.GetCurrenciesDelta()) : new JObject(),
                ["boosters"] = _playerManager?.Player?.Inventory?.Boosters != null ? JObject.FromObject(_playerManager.Player.Inventory.Boosters) : new JObject()
            };
        }

        private class LightPossibleMove
        {
            public LightCoords FirstCellCoords;
            public LightCoords SecondCellCoords;
        }

        private struct LightCoords
        {
            public int X;
            public int Y;
        }

        [FlayerFunction("set_level_progress", "Set desired level (last level won) for user's level progress", hintKey: "^\\d+$")]
        [Preserve]
        public static bool SetLevelProgress(int levelsToChange, PacketLogger packetLogger)
        {
            if (levelsToChange < 0)
            {
                packetLogger.Log("Invalid level to change", sendToAI: true);
                return false;
            }
            
            DebugMatch3PanelController.ChangeStageOnLocations(Stage.Complete,
                _playerManager.Player.PlayerDO.LevelStates, _locationManager, _playerManager, _accountManager, _promotionManager, _config,
                levelsToChange, lastScene: _playerManager.Player.CurrentEpisodeScene);
            return true;
        }
        
        [FlayerFunction("set_scene_progress", "Set desired scene and task number for user's scene progress", hintKey: "^\\d+$")]
        [Preserve]
        public static bool SetSceneProgress(string sceneUid, int taskNumber, PacketLogger packetLogger)
        {
            if (string.IsNullOrEmpty(sceneUid) || taskNumber < 0)
            {
                packetLogger.Log("Invalid scene or task number", sendToAI: true);
                return false;
            }

            var scenesConfig = _config.Get<ScenesConfig>();
            var allScenesConfig = scenesConfig.Values.OrderBy(x => x.UiOrder).ToList();

            DebugSceneCompletionPanelController.GoToTask(_playerManager, _episodicScenesManager, _episodeTaskManager,
                _eventDispatcher, allScenesConfig, sceneUid, taskNumber);
            return true;
        }
    }
}
#endif