using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using PBGame;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using FBConfig;
using GameAssets.Scripts.Map;
using GameAssets.Scripts.Map.UI.Controllers;
using GameAssets.Scripts.Player;
using JetBrains.Annotations;
using TMPro;
using BebopBee;
using GameAssets.Scripts.Promotions;

namespace BBB
{
    public class DebugMatch3PanelController : DebugPanelController
    {
        [SerializeField] private InputField _weeklyTrophiesInputField;
        [SerializeField] private InputField _butlerStreakInputField;

        [SerializeField] private InputField _numberOfLevelsToChange;
        [SerializeField] private Button _goToLevelButton;
        [SerializeField] private TMP_InputField _levelNumberInputField;
        [SerializeField] private Button _skipAutoPopups;

        private IDictionary<string, ProgressionLevelConfig> _levelConfig;

        private List<LevelState> _levelStates;
        private static int _maxLevelProgression;

        protected override void OnInit()
        {
            _levelConfig = DebugScreenController.Config.Get<ProgressionLevelConfig>();
            _levelStates = DebugScreenController.PlayerManager.Player.PlayerDO.LevelStates;
            _goToLevelButton.ReplaceOnClick(GoToLevelButtonHandler);
            _maxLevelProgression = DebugScreenController.Config.TryGetDefaultFromDictionary<SystemConfig>().MaxProgressionLevel;

            _skipAutoPopups.ReplaceOnClick(SkipAutoPopupHandler);
        }

        private void SkipAutoPopupHandler()
        {
#if BBB_DEBUG
            EpisodeScreenController.ForceSkipAutoPopups = true;
#endif
        }

        private bool TryParseTargetLevel(string levelText, out string levelUid, out int stageNumber)
        {
            levelUid = null;
            stageNumber = (int)Stage.Good + 1;

            if (levelText.IsNullOrEmpty())
            {
                Debug.LogError($"Empty level field");
                return false;
            }

            levelUid = levelText.Trim().ToLower();
            if (levelUid.Contains(":"))
            {
                var splittedStrings = levelUid.Split(":");
                levelUid = splittedStrings[0].Trim().ToLower();

                if (!int.TryParse(splittedStrings[1], out stageNumber))
                {
                    Debug.LogError($"Couldn't parse stage index: {splittedStrings[1]}");
                }
            }

            if (int.TryParse(levelUid, out var levelNumber))
            {
                if (levelNumber <= 0 || levelNumber > _levelStates.Count)
                {
                    Debug.LogError($"Level number {levelNumber} is out of range 1-{_levelStates.Count}");
                    return false;
                }

                levelUid = _levelStates[levelNumber - 1].SourceUid;
            }

            return true;
        }

        private void GoToLevelButtonHandler()
        {
            if (!TryParseTargetLevel(_levelNumberInputField.text, out var levelUid, out var stageNumber)) return;

            if (_levelConfig.ContainsKey(levelUid))
            {
                var levelNumber = Mathf.Max(_levelStates.IndexOf(x => x.SourceUid == levelUid), 0);

                ChangeStageOnLocations(Stage.Better, _levelStates,
                    DebugScreenController, levelNumber, false,
                    lastScene: DebugScreenController.PlayerManager.Player.CurrentEpisodeScene);

                var stage = Mathf.Clamp(stageNumber - 1, (int)Stage.Good, (int)Stage.Best);
                DebugScreenController.Starter.StartLevelWithStage(levelUid, stage);
            }
            else
            {
                Debug.LogError($"Couldn't find level with uid {levelUid}");
            }
        }

        [UsedImplicitly]
        public void OnWeeklyTrophiesAddClicked()
        {
            if (int.TryParse(_weeklyTrophiesInputField.text, out var trophies))
            {
                var weeklyLeaderboardManager = DebugScreenController.WeeklyLeaderboardManager;
                var totalTrophies = weeklyLeaderboardManager.TryAddWeeklyTrophy(trophies);
                if (totalTrophies > 0)
                {
                    weeklyLeaderboardManager.TrySubmitToWeeklyLeaderboard(success =>
                    {
                        if (success)
                        {
                            DebugScreenController.PlayerManager.MarkDirty(true);
                        }
                    });
                }
            }
        }

        [UsedImplicitly]
        public void OnButlerStreakUpdateClicked()
        {
            if (int.TryParse(_butlerStreakInputField.text, out var streak))
            {
                DebugScreenController.PlayerManager.Player.ButlerStreak = streak;
            }
        }

        [UsedImplicitly]
        public void CompleteLocationButtonHandler()
        {
            ChangeStageOnLocations(Stage.Better, _levelStates, DebugScreenController, GetNumberOfLevelsToChange(),
                lastScene: DebugScreenController.PlayerManager.Player.CurrentEpisodeScene);
        }

        private int GetNumberOfLevelsToChange()
        {
            if (int.TryParse(_numberOfLevelsToChange.text, out var numberOfLevels) && numberOfLevels >= 0)
                return numberOfLevels;
            return 0;
        }

        private static void ChangeStageOnLocations(Stage stage, List<LevelState> levelStates, DebugScreenController debugScreenController,
            int numberOfLevelsToChange = -1, bool filteredNumber = true, Action<ILocation> locationAction = null, string lastScene = EpisodicScenesManager.DefaultEpisodicScene)
        {
            ChangeStageOnLocations(stage, levelStates, 
                debugScreenController.LocationManager, debugScreenController.PlayerManager, debugScreenController.AccountManager, debugScreenController.PromotionManager,
                debugScreenController.Config, numberOfLevelsToChange, filteredNumber,
                locationAction, lastScene);
        }

        public static void ChangeStageOnLocations(Stage stage, List<LevelState> levelStates, 
            ILocationManager locationManager, IPlayerManager playerManager, IAccountManager accountManager, PromotionManager promotionManager,
            IConfig config, int numberOfLevelsToChange = -1, bool filteredNumber = true,
            Action<ILocation> locationAction = null, string lastScene = EpisodicScenesManager.DefaultEpisodicScene)
        {
            var distinctLocations = new HashSet<ILocation>();
            var stageIndex = (int)stage;

            ILocation lastLocation = null;
            LevelState lastLevelState = null;
            ChangeLevelsState((location, levelState) =>
            {
                levelState.Type = LevelStateType.AvailableToPlay.ToInt();
                location.SetLevelStateStage(levelState, Mathf.Max(levelState.Stage, stageIndex), true);
                levelState.FinishAllStages = stage == Stage.Complete;

                if (distinctLocations.Add(location))
                {
                    locationAction?.Invoke(location);
                }

                location.IncWins();
                lastLocation = location;
                lastLevelState = levelState;
            }, config, locationManager, playerManager,
            accountManager, levelStates, numberOfLevelsToChange, filteredNumber);

            if (lastLevelState != null)
            {
                var lastLevelPlayedData = new LastLevelPlayedData()
                {
                    LastPlayedMarkerUid = lastLevelState.SourceUid,
                    AnimationsBaseLevelUid = lastLevelState.SourceUid,
                    LevelStage = lastLevelState.Stage,
                    ProgressUpdate = false,
                    LevelOutcome = LevelOutcome.Win,
                    Location = lastLocation.Uid,
                    LevelUid = lastLevelState.SourceUid,
                    LastScenePlayed = lastScene
                };

                lastLevelPlayedData.Save();

                locationManager.OnLevelPassed(lastLevelState.SourceUid, true, 0, true);
            }

            var trophiesValidator = new TrophiesValidator(playerManager.Player.PlayerDO, accountManager.Profile,
                _maxLevelProgression, true);
            promotionManager.Refresh();
        }

        private static void ChangeLevelsState(Action<ILocation, LevelState> levelStateAction,
            IConfig config, ILocationManager locationManager, IPlayerManager playerManager,
            IAccountManager accountManager, List<LevelState> levelStates,
            int numberOfLevels = -1, bool filteredNumber = true)
        {
            var levelConfigs = config.Get<ProgressionLevelConfig>();
            var mainLocation = locationManager.MainProgressionLocation;
            var index = 0;
            var counter = numberOfLevels;
            while (counter > 0)
            {
                if (levelStates.Count <= index)
                {
                    var levelUid = $"level{index + 1}";
                    if (levelConfigs.ContainsKey(levelUid))
                    {
                        levelStates.Add(new LevelState { SourceUid = levelUid });
                    }
                    else
                        break; //reached the limit of levels by config
                }

                var levelState = levelStates[index];
                mainLocation.SetLevelStateStage(levelState, (int)Stage.Better, true);
                levelStateAction?.Invoke(mainLocation, levelState);
                mainLocation.IncWins();
                playerManager.AddStar();
                counter--;
                index++;
            }

            accountManager.Profile.HighestPassedLevelId = mainLocation.GetHighestPassedLevelUid();
        }
    }
}