using System;
using System.Collections.Generic;
using BBB.Core;
using BBB.Map;
using BBB.Match3.Systems;
using BBB.Wallet;
using GameAssets.Scripts.Match3.Logic;
using PBGame;
using UnityEngine;

namespace BBB.GameAssets.Scripts.Player
{
    public class Level : ILevel
    {
        private const string RegularResourceMultiplierResourceFormat = "icon_regular_{0}x";
        public int TurnsLimit { get; set; }
        public List<TileKinds> UsedKinds { get; set; }
        public GoalState Goals { get; set; }
        public Grid Grid { get; set; }
        public LitterSkin LitterSkin { get; set; }
        public StickerSkin StickerSkin { get; set; }
        public string AssistSystemUid { get; set; }

        public bool DoesUseTileKind(TileKinds kind)
        {
            if (UsedKinds != null)
                foreach (var usedKind in UsedKinds)
                {
                    if (kind == usedKind)
                    {
                        return true;
                    }
                }

            return false;
        }

        public AnimalSkin AnimalSkin => default;

        public GoalType OptionalGoalsEnabled { get; set; }

        public Hash128 AssetHash { get; set; }
        public string VersionHash { get; set; }

        private FBConfig.ProgressionLevelConfig _levelConfig;

        public FBConfig.ProgressionLevelConfig Config => _levelConfig;

        public string LevelName { get; private set; }
        public int TargetWinRate { get; private set; }
        public int TargetWinRateT2 { get; private set; }
        public int TargetWinRateT3 { get; private set; }


        private int _stageCount;

        public static readonly Dictionary<Stage, string> DificultyNameISOByStage = new()
        {
            { Player.Stage.Good, "STAGE_NAME_GOOD" },
            { Player.Stage.Better, "STAGE_NAME_BEST" },
            { Player.Stage.Best, "STAGE_NAME_BETTER" },
            { Player.Stage.Complete, "STAGE_NAME_COMPLETE" }
        };

        public static readonly Dictionary<Stage, string> DificultySubtitleISOByStage = new()
        {
            { Player.Stage.Good, "LEVEL_MODAL_GOOD_SUBTITLE" },
            { Player.Stage.Better, "LEVEL_MODAL_BETTER_SUBTITLE" },
            { Player.Stage.Best, "LEVEL_MODAL_BEST_SUBTITLE" },
            { Player.Stage.Complete, "" }
        };


        public static readonly Dictionary<Stage, string> DifficultyDescriptionISOByStage = new()
        {
            { Player.Stage.Good, "LEVEL_MODAL_GOOD_MESSAGE" },
            { Player.Stage.Better, "LEVEL_MODAL_BETTER_MESSAGE" },
            { Player.Stage.Best, "LEVEL_MODAL_BEST_MESSAGE" },
            { Player.Stage.Complete, "LEVEL_MODAL_COMPLETE_MESSAGE" }
        };

        private LevelState _state;

        public int Stage
        {
            get
            {
                if (_state == null)
                {
                    Debug.LogError("Current level does not have protobuf state setup");
                    return 0;
                }

                return _state.Stage;
            }
        }

        public int GrindReplays
        {
            get
            {
                if (_state == null)
                {
                    Debug.LogError("Current level does not have protobuf state setup");
                    return 0;
                }

                return _state.GrindReplays;
            }
        }

        public Dictionary<string, int> RewardsDictionary { get; private set; }

        public string GetStageCurrency()
        {
            if (_state.GrindReplays != 0)
                return string.Empty;

            return WalletResources.HardCurrency;
        }

        public Level()
        {
        }
        
        public string LevelUid => _levelConfig.Uid;
        public int LossStreak => _state.LossStreak;

        public static int GetStageCount(string uid)
        {
            return 3;
        }

        public void SetupRemoteData(LevelRemoteData remoteData, SpawnerSettings[] globalSpawners, bool allowAutoFixGoals = true)
        {
            _levelConfig = remoteData.Config;
            _state = remoteData.State;
            TurnsLimit = remoteData.Turns;
            _stageCount = GetStageCount(_levelConfig.Uid);
            LevelName = _levelConfig.GetLevelName();
            TargetWinRate = _levelConfig.TrueTargetWinRate();
            TargetWinRateT2 = _levelConfig.TrueTargetWinRateT2();
            TargetWinRateT3 = _levelConfig.TrueTargetWinRateT3();
            OptionalGoalsEnabled = remoteData.OptionalGoals;

            // Todo: Remove mandatory goals after all levels will be updated. -VK
            OptionalGoalsEnabled = CheckMandatoryGoals(OptionalGoalsEnabled, Grid);
            Goals.SetupRemoteData(Grid, UsedKinds, remoteData.GetGoalData());
            Goals.InitializeTargetGoalCountsIfNotManuallySpecified(Grid, _levelConfig.Uid, OptionalGoalsEnabled, globalSpawners);

            if (allowAutoFixGoals)
            {
                Goals.ResetGoalsWithMissingSpawnersIfNeeded(Grid, _levelConfig.Uid, globalSpawners);
            }

            VersionHash = _levelConfig.Hash;
            SetupRewards(remoteData);
        }

        /// <summary>
        /// Add mandatory goals at runtime if Grid contains items related to these goals.
        /// This is temporary solution, it should be removed in future (all goals should be non-mandatory).
        /// Todo: Remove this when all goals will be added to levels config. -VK
        /// </summary>
        private GoalType CheckMandatoryGoals(GoalType goals, Grid grid)
        {
            if ((goals & GoalType.DropItems) == 0 && grid.HasDropItems())
            {
                // Backward support of old levels, where drop items goals is not specified explicitly.
                goals |= GoalType.DropItems;
            }

            if (GoalState.HasCellRelatedToGoal(grid, GoalType.Backgrounds))
            {
                goals |= GoalType.Backgrounds;
            }

            if (GoalState.HasCellRelatedToGoal(grid, GoalType.Petal))
            {
                goals |= GoalType.Petal;
            }

            if (GoalState.HasCellRelatedToGoal(grid, GoalType.DestructibleWall))
            {
                goals |= GoalType.DestructibleWall;
            }

            if (GoalState.HasCellRelatedToGoal(grid, GoalType.Pinata))
            {
                goals |= GoalType.Pinata;
            }

            if (GoalState.HasCellRelatedToGoal(grid, GoalType.Animal))
            {
                goals |= GoalType.Animal;
            }

            return goals;
        }

        public static string StageToResouceIconId(Stage stage)
        {
            int stageInt = (int)stage + 1;
            if (stage == Player.Stage.Complete)
            {
                stageInt = (int)stage;
            }

            return string.Format(RegularResourceMultiplierResourceFormat, stageInt);
        }

        private void SetupRewards(LevelRemoteData remoteData)
        {
            var config = remoteData.Config;
            var state = remoteData.State;

            var rewardStr = config.GetTrueReward();

            if (rewardStr == null)
            {
                Debug.LogErrorFormat("No rewards found in level config {0} for stage {1}", config.Uid, state.Stage);
                return;
            }

            RewardsDictionary = RewardsUtility.RewardStringToDict(rewardStr).FilterRewards();
        }

        public bool HasAnyResultRecords
        {
            get
            {
                if (_state.LossResultTail != null)
                {
                    foreach (var goalResult in _state.LossResultTail)
                    {
                        if (goalResult.GoalCountsMap != null)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
        }

        public GoalState AverageResult
        {
            get
            {
                if (_state.LossResultTail == null)
                    throw new Exception("Level was never lost to have any loss result");

                int count = 0;

                foreach (var goalResult in _state.LossResultTail)
                {
                    if (goalResult.GoalCountsMap != null)
                    {
                        count++;
                    }
                }

                if (count == 0)
                    throw new Exception("Level results collection is empty, which is not supposed to be");

                GoalState sumOfResults = new GoalState();

                foreach (var result in _state.LossResultTail)
                {
                    if (result.GoalCountsMap == null)
                    {
                        Debug.LogWarning($"GoalsCountMap is null for level {_levelConfig.Uid}, this is not supposed to happen");
                        continue;
                    }

                    var goalStateResult = new GoalState(result);
                    sumOfResults += goalStateResult;
                }

                sumOfResults.Multiply(1f / count);

                return sumOfResults;
            }
        }

        public void WinLevel(ILocationManager locationManager)
        {
            if (_state.Stage < _stageCount)
            {
                locationManager.MainProgressionLocation.SetLevelStateStage(_state, _state.Stage + 1);
            }
            else
            {
                _state.GrindReplays++;
            }
            
            locationManager.MainProgressionLocation.SetLastLevelPlayed(_state.SourceUid);

            _state.FinishAllStages = _state.Stage >= _stageCount;
            _state.LossStreak = 0;
        }

        public void LoseLevel(IConfig config)
        {
            IncreaseLossStreak();
        }

        public void ExitWithoutPlaying()
        {
        }

        private void IncreaseLossStreak()
        {
            _state.LossStreak++;
        }

        public static string GetLevelFullName(FBConfig.ProgressionLevelConfig config, int stage)
        {
            var fileNames = config.TrueFileNames();
            if (fileNames == null)
                return string.Empty;

            var name = fileNames[Math.Min(stage, fileNames.Count - 1)];
            return $"{GetLevelFilePath(config)}/{name}";
        }

        public static string GetLevelFilePath(FBConfig.ProgressionLevelConfig config)
        {
            return config.LocationUid ?? config.GetMainProgressionFolder();
        }

        public string GetLevelFullName()
        {
            return GetLevelFullName(Config, _state.Stage);
        }

        public static string GetLevelName(FBConfig.ProgressionLevelConfig config, int stage)
        {
            var trueFileNames = config.TrueFileNames();
            return trueFileNames[Math.Min(stage, trueFileNames.Count - 1)];
        }

        public float GetNum(IConfig config)
        {
            return _levelConfig.ReportingLevelNum;
        }

        public string GetDifficultyLocalizationID()
        {
            var stage = this.GetPaletteStage();
            return DificultyNameISOByStage[stage];
        }

        public void IncrementNumPlayed()
        {
            _state.NumTimesPlayed ??= new int[3];

            var index = Mathf.Clamp(_state.Stage, 0, 2);

            _state.NumTimesPlayed[index]++;
        }
        
        public void DecrementNumPlayed()
        {
            _state.NumTimesPlayed ??= new int[3];

            var index = Mathf.Clamp(_state.Stage, 0, 2);

            _state.NumTimesPlayed[index] = Mathf.Max(0, _state.NumTimesPlayed[index] - 1);
        }

        public int GetNumPlayed()
        {
            if (_state.NumTimesPlayed == null)
                return 0;

            var index = Mathf.Clamp(_state.Stage, 0, 2);
            return _state.NumTimesPlayed[index];
        }

        public void IncreaseWinRate(int amount)
        {
            TargetWinRate += amount;
            TargetWinRateT2 += amount;
            TargetWinRateT3 += amount;
        }
    }
}