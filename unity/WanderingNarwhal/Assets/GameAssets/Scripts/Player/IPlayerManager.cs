using BBB;
using BBB.Core.Wallet;
using BBB.Match3.Systems.CreateSimulationSystems;
using Cysharp.Threading.Tasks;
using FBConfig;
using GameAssets.Scripts.Player;

public interface IPlayerManager : IWalletTransactionObserver 
{
    IPlayer Player { get; }
    IInventory PlayerInventory { get; }

    /// <summary>
    /// Current playing location (not always last unlocked).
    /// </summary>
    ILevel CurrentLevel { get; }
    bool DisableSave { get; set; }
    UniTask<(bool, PlayerSavedGame)> TryToSave(bool force);
    void SetCurrentLevel(ILevel level);
    int StarCount { get; }
    void AddStar(int amount = 1);
    void SpendStar(int amount);
    
    #region Player Skill
    float GetOnAimToWinSkillModifier();
    float GetOnAimToLoseSkillModifier();
    void SetOnAimToWinSkillModifier(float value);
    void SetOnAimToLoseSkillModifier(float value);
    void OnLevelEnded(ILevel level, LevelOutcome levelOutcome, int movesLeft, int plus5MovesUsed,
        AssistParams assistParams);
    void RecordIap(IAPStoreMarketItemConfig iapConfig);
    void MarkDirty(bool force = false);

    #endregion
}