using System.Collections.Generic;
using BBB;
using BBB.Core;
using BBB.Core.Analytics.TechAnalytics.Managers;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Systems;
using BBB.Screens;
using FBConfig;

namespace GameAssets.Scripts.Map.UI.Controllers
{
    public class LevelStarter : IContextInitializable
    {
        private ILocationManager _locationManager;
        protected IBoosterManager BoosterManager;
        protected IScreensBuilder ScreensBuilder;
        protected IPlayerManager PlayerManager;
        protected IConfig Config;
        protected IScreensManager ScreensManager;
        private IContext _context;
        protected LevelRemoteData _currentLevelRemoteData;
        private readonly LoadLevelAsyncCommand _loadLevelAsyncCommand = new();
        private string _lastLevelUid;

        public virtual void InitializeByContext(IContext context)
        {
            _context = context;
            _locationManager = context.Resolve<ILocationManager>();
            BoosterManager = context.Resolve<IBoosterManager>();
            ScreensBuilder = context.Resolve<IScreensBuilder>();
            ScreensManager = context.Resolve<IScreensManager>();
            Config = context.Resolve<IConfig>();
            PlayerManager = context.Resolve<IPlayerManager>();
        }

        public void StartLevelWithStage(string levelUid, int stage)
        {
            var levelConfigs = Config.Get<ProgressionLevelConfig>();
            var levelConfig = levelConfigs[levelUid];

            var levelData = new LevelRemoteData(levelConfig, stage);
            var screenType = GetTargetLevelScreen(levelData);
            
            LoadingProcessTracker.LogShowScreen(screenType.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "StartLevelWithStage");
            ScreensBuilder.ShowScreen(screenType, new LoadLevelAsyncCommand(levelData));
        }

        public void PreloadLevel(string levelUid)
        {
            if (_lastLevelUid == levelUid && _currentLevelRemoteData != null) 
                return;

            _lastLevelUid = levelUid;
            _currentLevelRemoteData = GetLevelRemoteData(levelUid);
            _loadLevelAsyncCommand.ResetCommand()
                .SetLeveRemoteData(_currentLevelRemoteData)
                .Execute(_context);
        }

        public void ResetLevel()
        {
            _lastLevelUid = string.Empty;
            _currentLevelRemoteData = null;
        }

        protected virtual LevelRemoteData GetLevelRemoteData(string levelUid)
        {
            var levelConfigs = Config.Get<ProgressionLevelConfig>();
            if (!levelConfigs.TryGetValue(levelUid, out var levelConfig)) 
            {
                BDebug.LogError(LogCat.General, $"Level config {levelUid} not found");
                return null;
            }
            var levelState = _locationManager.MainProgressionLocation.GetLevelState(levelUid);

            if (levelState == null)
            {
                BDebug.LogError(LogCat.General, $"Level state {levelUid} not found");
                return null;
            }

            return new LevelRemoteData(levelConfig, levelState, false);
        }

        public virtual void StartLevel(string levelUid)
        {
            PreloadLevel(levelUid);

            var screenType = GetTargetLevelScreen(_currentLevelRemoteData);
            LoadingTimeMetricsManager.LevelLoadingStarted();
            LoadingProcessTracker.LogShowScreen(screenType.ToString(), ScreensManager.GetTrackingPreviousScreenType(), "StartLevel");
            ScreensBuilder.ShowScreen(screenType);
            if (BoosterManager.StartedLevelWithBoosters(PlayerManager.PlayerInventory.EquippedAutoBoosters))
            {
                BoosterManager.GameStarted = true;
            }
        }

        private ScreenType GetTargetLevelScreen(LevelRemoteData levelData)
        {
            return ScreenType.LevelScreen;
        }
    }
}