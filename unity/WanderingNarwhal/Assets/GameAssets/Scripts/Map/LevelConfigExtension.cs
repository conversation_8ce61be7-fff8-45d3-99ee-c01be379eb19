using System.Collections.Generic;
using System.Globalization;
using System.IO;
using BBB.Core;
using BBB.GameAssets.Scripts.Player;
using BBB.M3Editor;
using BBB.Match3.Systems;
using BBB.Navigation;
using Core.Configs;
using FBConfig;
using Google.FlatBuffers;
using PBGame;
using UnityEngine;

namespace BBB.Map
{
    public static class LevelConfigExtension
    {
        private static LevelsOrderingManager _orderingManager;

        public static void SetOrdering(LevelsOrderingManager levelOrderingManager)
        {
            _orderingManager = levelOrderingManager;
        }

        public static int GetIndex(this ProgressionLevelConfig levelConfig)
        {
            return levelConfig.Uid.GetIntNumber();
        }

        public static int GetIndex(this LevelState levelState)
        {
            return levelState.SourceUid.GetIntNumber();
        }

        private static Stage DifficultyToStage(this int difficulty)
        {
            // that's intentional order change
            var stage = difficulty switch
            {
                0 => Stage.Good,
                1 => Stage.Best,
                2 => Stage.Better,
                3 => Stage.Complete,
                _ => Stage.Good
            };

            return stage;
        }

        public static Stage GetPaletteStage(this ProgressionLevelConfig level)
        {
            return DifficultyToStage(level.Difficulty);
        }

        public static Stage GetPaletteStage(this ILevel level)
        {
            return DifficultyToStage(level.Config.Difficulty);
        }

        public static Level LoadLevel(this ProgressionLevelConfig levelConfig, int stageIndex, SpawnerSettings[] globalSpawners)
        {
            var trueFileNames = levelConfig.TrueFileNames();
            if (trueFileNames.Count <= stageIndex)
                return null;

            var path = "Levels/" + Level.GetLevelFilePath(levelConfig) + "/" + trueFileNames[stageIndex] + ".bytes";
            var levelFullPath = Path.Combine(Application.dataPath, path);

            M3SaveLoadUtility.LoadLevelFromFile(levelFullPath, out var level);
            level.SetupRemoteData(new LevelRemoteData(levelConfig, stageIndex), globalSpawners);

            return level;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        public static List<string> TrueStartupBoosts(this ProgressionLevelConfig levelConfig)
        {
            if (levelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig)) return null;
            if (levelConfig.StartupBoostsLength > 0) return FlatBufferHelper.ToList(levelConfig.StartupBoosts, levelConfig.StartupBoostsLength);

            var foundLevelConfig = _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => cfg.StartupBoostsLength == 0);
            return FlatBufferHelper.ToList(foundLevelConfig.StartupBoosts, foundLevelConfig.StartupBoostsLength);
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        public static List<string> TrueEligibleBoosts(this ProgressionLevelConfig levelConfig)
        {
            var foundLevelConfig =
                _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => cfg.EligibleBoostsLength == 0);

            return FlatBufferHelper.ToList(foundLevelConfig.EligibleBoosts, foundLevelConfig.EligibleBoostsLength);
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        private static string TrueGoodReward(this ProgressionLevelConfig levelConfig)
        {
            var foundLevelConfig =
                _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => cfg.GoodReward == null);

            return foundLevelConfig.GoodReward;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        private static string TrueBetterReward(this ProgressionLevelConfig levelConfig)
        {
            var foundLevelConfig =
                _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => string.IsNullOrWhiteSpace(cfg.BetterReward));

            return foundLevelConfig.BetterReward;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        private static string TrueBestReward(this ProgressionLevelConfig levelConfig)
        {
            var foundLevelConfig =
                _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => string.IsNullOrWhiteSpace(cfg.BestReward));

            return foundLevelConfig.BestReward;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        private static string TrueCompleteReward(this ProgressionLevelConfig levelConfig)
        {
            var foundLevelConfig =
                _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => string.IsNullOrWhiteSpace(cfg.CompleteReward));

            return foundLevelConfig.CompleteReward;
        }

        private static List<string> _cachedRewardKeys = new();

        public static string GetTrueReward(this ProgressionLevelConfig levelConfig)
        {
            var difficulty = levelConfig.Difficulty;
            var configReward = difficulty switch
            {
                0 => levelConfig.TrueGoodReward(),
                1 => levelConfig.TrueBetterReward(),
                2 => levelConfig.TrueBestReward(),
                3 => levelConfig.TrueCompleteReward(),
                _ => levelConfig.TrueCompleteReward()
            };

            // var difficultyMultiplier = levelConfig.Difficulty switch
            // {
            //     0 => 0,
            //     1 => 25,
            //     2 => 50,
            //     _ => 0
            // };
            //
            // var rewardMultiplier = (100 + difficultyMultiplier) * 0.01f;
            //
            // var configReward = stage switch
            // {
            //     Stage.Good => levelConfig.TrueGoodReward(),
            //     Stage.Better => levelConfig.TrueBetterReward(),
            //     Stage.Best => levelConfig.TrueBestReward(),
            //     Stage.Complete => levelConfig.TrueCompleteReward(),
            //     _ => levelConfig.TrueCompleteReward()
            // };
            //
            // if (difficultyMultiplier > 0)
            // {
            //     var rewardDictionary = RewardsUtility.RewardStringToDict(configReward);
            //     
            //     _cachedRewardKeys.Clear();
            //     foreach (var currencyUid in rewardDictionary.Keys)
            //         _cachedRewardKeys.Add(currencyUid);
            //     
            //     foreach (var currencyUid in _cachedRewardKeys)
            //     {
            //         if (rewardDictionary[currencyUid] > 1)
            //         {
            //             rewardDictionary[currencyUid] = Mathf.RoundToInt(rewardDictionary[currencyUid] * rewardMultiplier);
            //         }
            //     }
            //
            //     configReward = RewardsUtility.RewardDictToString(rewardDictionary);
            // }

            return configReward;
        }

        public static int GetRewardMultiplier(this ProgressionLevelConfig levelConfig, Stage stage)
        {
            var difficultyMultiplier = levelConfig.Difficulty switch
            {
                0 => 0,
                1 => 25,
                2 => 50,
                _ => 0
            };

            var stageMultiplier = stage switch
            {
                Stage.Good => 0,
                Stage.Better => 25,
                Stage.Best => 50,
                Stage.Complete => 0,
                _ => 0
            };

            return difficultyMultiplier + stageMultiplier;
        }
        
        public static List<string> TrueFileNames(this ProgressionLevelConfig levelConfig)
        {
            if (levelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig))
                return null;
            
            if (levelConfig.FileNamesLength > 0)
                return FlatBufferHelper.ToList(levelConfig.FileNames, levelConfig.FileNamesLength);

            if (levelConfig.Uid == null)
                return null;

            var digitName = levelConfig.LocationUid.IsNullOrEmpty()
                ? levelConfig.SortOrder.ToString(CultureInfo.InvariantCulture)
                : levelConfig.Uid.Replace(levelConfig.LocationUid, string.Empty);
            if (digitName.Length == 1)
                digitName = "0" + digitName;

            var runtimeFileNames = new List<string>();
            for (int i = 1; i < 4; i++)
            {
                var fileName = $"level_{digitName}_0{i}.M3L";
                runtimeFileNames.Add(fileName);
            }

            return runtimeFileNames;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        public static int TrueTargetWinRate(this ProgressionLevelConfig levelConfig)
        {
            if (levelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig)) return 100;
            if (levelConfig.TargetWinRate > 0) return levelConfig.TargetWinRate;

            var foundLevelConfig = _orderingManager.GetFirstWhereFalse(levelConfig.Uid, cfg => cfg.TargetWinRate == 0);
            if (foundLevelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig)) return 100;
            return foundLevelConfig.TargetWinRate;
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        public static int TrueTargetWinRateT2(this ProgressionLevelConfig levelConfig)
        {
            if (levelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig)) return 100;
            if (levelConfig.TargetWinRateT2 > 0) return levelConfig.TargetWinRateT2;

            //fallback to the first win rate if T2 is empty
            return TrueTargetWinRate(levelConfig);
        }

        /// <summary>
        /// Purpose of this method is to keep less data in the config and take value from prev config if current is null 
        /// </summary>
        public static int TrueTargetWinRateT3(this ProgressionLevelConfig levelConfig)
        {
            if (levelConfig.Equals(FlatBufferHelper.DefaultProgressionLevelConfig)) return 100;
            if (levelConfig.TargetWinRateT3 > 0) return levelConfig.TargetWinRateT3;

            //fallback to the 2nd win rate if T3 is empty
            return TrueTargetWinRateT2(levelConfig);
        }

        public static ProgressionLevelConfig CreateLevelConfig(ProgressionLevelConfigT dummy)
        {
            var fbb = new FlatBufferBuilder(0x100);
            var offset = ProgressionLevelConfig.Pack(fbb, dummy);
            fbb.Finish(offset.Value);

            var bytes = fbb.SizedByteArray();
            var buf = new ByteBuffer(bytes);
            return ProgressionLevelConfig.GetRootAsProgressionLevelConfig(buf);
        }

        /// <summary>
        /// Iterate over location's levels (without cache), use ILevelsOrderingManager if possible
        /// </summary>
        public static IEnumerable<ProgressionLevelConfig> GetLocationLevels(
            this IDictionary<string, ProgressionLevelConfig> config, string locationUid)
        {
            var index = 1;
            var name = $"{locationUid}{index}";
            while (config.TryGetValue(name, out var levelConfig))
            {
                yield return levelConfig;
                index++;
                name = $"{locationUid}{index}";
            }
        }

        public static int GetMainProgressionTier(this ProgressionLevelConfig config, uint splitAmount = 50)
        {
            return (int)((config.SortOrder - 1) / splitAmount);
        }
        
        /// <summary>
        /// Get folder for main progression level
        /// We split them by amount, the folder will have name main_ + the order of the first level in tier
        /// For example: main_1, main_51, main_101, ... for 50 levels per tier
        /// </summary>
        public static string GetMainProgressionFolder(this ProgressionLevelConfig config, uint splitAmount = 50)
        {
            var firstLevelInTier = config.GetMainProgressionTier(splitAmount) * splitAmount + 1;
            return $"main_{firstLevelInTier}";
        }
    }
}