#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using BBB;
using BBB.M3Editor;
using BBB.Match3;
using BBB.Match3.Systems.CreateSimulationSystems;

namespace GameAssets.Scripts.Match3.Debug.M3Editor.Tools
{
    public sealed class SkunkTileEditorTool : TileTool
    {
        public SkunkTileEditorTool(Dictionary<Type, IM3EditorSystem> m3Systems) : base(m3Systems)
        {
        }

        public override void Apply(Grid grid, Coords coords,
            CardinalDirections cardinalDirections, int prm)
        {
            var cell = M3EditorTile.GetGridCell(coords);
            if (cell == null) return;
            cell.HardRemoveTile(0);
            List<TileParam> tileParams = null;
            if (prm > 0)
            {
                tileParams = new List<TileParam> { new() { Param = TileParamEnum.RestoresCount, Value = prm } };
            }
            
            var newTile = TileFactory.CreateTile(grid.TilesSpawnedCount++, TileAsset.Skunk, new TileOrigin(Creator.LevelEditor, cell),
                TileKinds.None, tileParams);
            cell.AddTile(newTile);
            base.Apply(grid, coords, cardinalDirections, prm);
        }
    }
}
#endif
