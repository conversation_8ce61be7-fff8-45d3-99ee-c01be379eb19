using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Systems;

namespace BBB.GameAssets.Scripts.Match3.Logic
{
    public class GridStats
    {
        private static readonly List<TileSpeciality> SpecsToCount = new List<TileSpeciality>
        {
            TileSpeciality.Litter,
            TileSpeciality.<PERSON>er,
            TileSpeciality.Pinata,
            TileSpeciality.DropItem,
            TileSpeciality.Frame,
            TileSpeciality.ColorCrate,
            TileSpeciality.Watermelon,
            TileSpeciality.MoneyBag,
            TileSpeciality.Penguin,
            TileSpeciality.Egg,
            TileSpeciality.Bird,
            TileSpeciality.Sheep,
            TileSpeciality.Banana,
            TileSpeciality.Monkey,
            TileSpeciality.BigMonkey,
            TileSpeciality.Skunk,
            TileSpeciality.Hen,
            TileSpeciality.Chicken,
            TileSpeciality.Hive,
            TileSpeciality.Bee,
            TileSpeciality.<PERSON><PERSON>,
            TileSpeciality.Bowling,
            TileSpeciality.Bush,
            TileSpeciality.Soda,
            TileSpeciality.Safe,
            TileSpeciality.FlowerPot,
            TileSpeciality.IceBar,
            TileSpeciality.DynamiteBox,
            TileSpeciality.GiantPinata,
            TileSpeciality.MetalBar,
            TileSpeciality.Shelf,
            TileSpeciality.JellyFish,
            TileSpeciality.GoldenScarab,
            TileSpeciality.FireWorks,
            TileSpeciality.SlotMachine,
            TileSpeciality.Stone
        };

        private static readonly List<CellState> CellStateToCount = new List<CellState>
        {
            CellState.Ivy
        };

        private readonly Dictionary<TileSpeciality, int> _specCounts = new Dictionary<TileSpeciality, int>();
        private readonly Dictionary<TileState, int> _modCounts = new Dictionary<TileState, int>();
        private readonly Dictionary<CellState, int> _cellStateCounts = new Dictionary<CellState, int>();

        public GridStats(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                if (!ReferenceEquals(cell.Tile, null))
                {
                    if (SpecsToCount.Contains(cell.Tile.Speciality))
                    {
                        if (_specCounts.TryGetValue(cell.Tile.Speciality, out var count))
                        {
                            _specCounts[cell.Tile.Speciality] = count + 1;
                        }
                        else
                        {
                            _specCounts.Add(cell.Tile.Speciality, 1);
                        }
                    }

                    foreach (var mod in TileStateExtensions.ModStates)
                    {
                        if (cell.Tile.IsAnyOf(mod))
                        {
                            if (_modCounts.TryGetValue(mod, out var count))
                            {
                                _modCounts[mod] = count + 1;
                            }
                            else
                            {
                                _modCounts.Add(mod, 1);
                            }
                        }
                    }

                    foreach (var cellState in CellStateToCount)
                    {
                        if (cell.IsAnyOf(cellState))
                        {
                            if (_cellStateCounts.TryGetValue(cellState, out var count))
                            {
                                _cellStateCounts[cellState] = count + 1;
                            }
                            else
                            {
                                _cellStateCounts.Add(cellState, 1);
                            }
                        }

                    }
                }
            }
        }

        public IEnumerable<ConsequenceType> GetConsequences()
        {
            var consequences = new List<ConsequenceType>();

            foreach (var spec in SpecsToCount)
            {
                if (_specCounts.TryGetValue(spec, out var count) && count > 0)
                {
                    consequences.AddRange(SpecToConsequences(spec));
                }
            }

            foreach (var mod in TileStateExtensions.ModStates)
            {
                if (_modCounts.TryGetValue(mod, out var count) && count > 0)
                {
                    consequences.AddRange(ModToConsequences(mod));
                }
            }

            foreach (var cs in CellStateToCount)
            {
                if (_cellStateCounts.TryGetValue(cs, out var count) && count > 0)
                {
                    consequences.AddRange(CellStateToConsequences(cs));
                }
            }

            consequences.AddRange(UnconditionalConsequences());

            return consequences;
        }

        private IEnumerable<ConsequenceType> ModToConsequences(TileState mod)
        {
            switch (mod)
            {
                case TileState.IceCubeMod:
                    yield return ConsequenceType.IceCubeLayerDestroy;
                    break;
                case TileState.ChainMod:
                    yield return ConsequenceType.ChainLayerDestroyed;
                    break;
                case TileState.SandMod:
                    yield return ConsequenceType.SandLayerDestroyed;
                    break;
                case TileState.VaseMod:
                    yield return ConsequenceType.VaseLayerDestroy;
                    break;
                case TileState.EggMod:
                    yield return ConsequenceType.EggLayerDestroy;
                    break;
                case TileState.HenMod:
                    yield return ConsequenceType.HenDestroy;
                    break;
                case TileState.ChickenMod:
                    yield return ConsequenceType.ChickenGoal;
                    break;
                case TileState.HiveMod:
                    yield return ConsequenceType.HiveDestroy;
                    break;
                case TileState.BeeMod:
                    yield return ConsequenceType.BeeGoal;
                    break;
                case TileState.SquidMod:
                    yield return ConsequenceType.SquidGoal;
                    break;
                case TileState.ToadMod:
                    yield return ConsequenceType.ToadGoal;
                    break;
                case TileState.MagicHatMod:
                    yield return ConsequenceType.MagicHatGoal;
                    break;
                case TileState.BowlingMod:
                    yield return ConsequenceType.BowlingPin;
                    break;
                case TileState.BushMod:
                    yield return ConsequenceType.BushGoal;
                    break;
                case TileState.SodaMod:
                    yield return ConsequenceType.SodaBottle;
                    break;
                case TileState.SafeMod:
                    yield return ConsequenceType.SafeGoal;
                    break;
                case TileState.FlowerPotMod:
                    yield return ConsequenceType.FlowerPotLayerDestroy;
                    break;
                case TileState.IceBarMod:
                    yield return ConsequenceType.IceBarGoal;
                    break;
                case TileState.DynamiteBoxMod:
                    yield return ConsequenceType.DynamiteStick;
                    break;
                case TileState.GiantPinataMod:
                    yield return ConsequenceType.GiantPinataGoal;
                    break;
                case TileState.MetalBarMod:
                    yield return ConsequenceType.MetalBarGoal;
                    break;
                case TileState.ShelfMod:
                    yield return ConsequenceType.Shelf;
                    break;
                case TileState.JellyFishMod:
                    yield return ConsequenceType.JellyFish;
                    break;
                case TileState.GoldenScarabMod:
                    yield return ConsequenceType.GoldenScarab;
                    break;
                case TileState.GondolaMod:
                    yield return ConsequenceType.Gondola;
                    break;
                case TileState.TukTukMod:
                    yield return ConsequenceType.TukTuk;
                    break;
                case TileState.FireWorksMod:
                    yield return ConsequenceType.FireWorks;
                    break;
                case TileState.SlotMachineMod:
                    yield return ConsequenceType.SlotMachine;
                    break;
            }
        }

        private IEnumerable<ConsequenceType> CellStateToConsequences(CellState cellState)
        {
            switch (cellState)
            {
                case CellState.Ivy:
                    yield return ConsequenceType.IvyLayerDestroyed;
                    break;
            }
        }

        private IEnumerable<ConsequenceType> SpecToConsequences(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.Litter:
                    yield return ConsequenceType.LitterDestroy;
                    break;
                case TileSpeciality.Sticker:
                    yield return ConsequenceType.StickerLayerDestroy;
                    break;
                case TileSpeciality.Pinata:
                    yield return ConsequenceType.PinataEndangered;
                    break;
                case TileSpeciality.DropItem:
                    yield return ConsequenceType.DropItemWentDown;
                    yield return ConsequenceType.DropItemMovedOverDespawner;
                    break;
                case TileSpeciality.Frame:
                    yield return ConsequenceType.FrameDestroy;
                    break;
                case TileSpeciality.ColorCrate:
                    yield return ConsequenceType.ColorCrateLayerDestroy;
                    yield return ConsequenceType.ColorCrateEndangered;
                    break;
                case TileSpeciality.Watermelon:
                    yield return ConsequenceType.WatermelonLayerDestroy;
                    break;
                case TileSpeciality.Egg:
                    yield return ConsequenceType.EggLayerDestroy;
                    break;
                case TileSpeciality.Hen:
                    yield return ConsequenceType.HenDestroy;
                    break;
                case TileSpeciality.Chicken:
                    yield return ConsequenceType.ChickenGoal;
                    break;
                case TileSpeciality.Hive:
                    yield return ConsequenceType.HiveDestroy;
                    break;
                case TileSpeciality.Bee:
                    yield return ConsequenceType.BeeGoal;
                    break;
                case TileSpeciality.Mole:
                    yield return ConsequenceType.MoleGoal;
                    break;
                case TileSpeciality.Squid:
                    yield return ConsequenceType.SquidGoal;
                    break;
                case TileSpeciality.Toad:
                    yield return ConsequenceType.ToadGoal;
                    break;
                case TileSpeciality.MagicHat:
                    yield return ConsequenceType.MagicHatGoal;
                    break;
                case TileSpeciality.Bowling:
                    yield return ConsequenceType.BowlingPin;
                    break;
                case TileSpeciality.Bush:
                    yield return ConsequenceType.BushGoal;
                    break;
                case TileSpeciality.Soda:
                    yield return ConsequenceType.SodaBottle;
                    break;
                case TileSpeciality.Safe:
                    yield return ConsequenceType.SafeGoal;
                    break;
                case TileSpeciality.FlowerPot:
                    yield return ConsequenceType.FlowerPotLayerDestroy;
                    break;
                case TileSpeciality.IceBar:
                    yield return ConsequenceType.IceBarGoal;
                    break;
                case TileSpeciality.DynamiteBox:
                    yield return ConsequenceType.DynamiteStick;
                    break;
                case TileSpeciality.GiantPinata:
                    yield return ConsequenceType.GiantPinataGoal;
                    break;
                case TileSpeciality.MetalBar:
                    yield return ConsequenceType.MetalBarGoal;
                    break;
                case TileSpeciality.Shelf:
                    yield return ConsequenceType.Shelf;
                    break;
                case TileSpeciality.JellyFish:
                    yield return ConsequenceType.JellyFish;
                    break;
                case TileSpeciality.GoldenScarab:
                    yield return ConsequenceType.GoldenScarab;
                    break;
                case TileSpeciality.Gondola:
                    yield return ConsequenceType.Gondola;
                    break;
                case TileSpeciality.TukTuk:
                    yield return ConsequenceType.TukTuk;
                    break;
                case TileSpeciality.FireWorks:
                    yield return ConsequenceType.FireWorks;
                    break;
                case TileSpeciality.SlotMachine:
                    yield return ConsequenceType.SlotMachine;
                // case TileSpeciality.Stone:
                //     yield return ConsequenceType.StonDestroy;
                    break;
            }
        }

        private IEnumerable<ConsequenceType> UnconditionalConsequences()
        {
            yield return ConsequenceType.ColorBombCreated;
            yield return ConsequenceType.BombCreated;
            yield return ConsequenceType.LineBreakerCreated;
            yield return ConsequenceType.ColorBombUsed;
        }
    }
}
