using BBB.UI;

namespace BBB.Match3.Renderer
{
    public enum FxType
    {
        LineBreaker                 = 0,
        TileRemove                  = 4,
        ScoreText                   = 5,
        TripleLineBreaker           = 6,
        Whirlpool                   = 7,
        DoubleLineBreaker           = 8,
        Match                       = 9,
        WindTrail                   = 12, //power up dependent
        Shovel                      = 14, //power up dependent
        Balloon                     = 15, //layout dependent
        LightningBolt               = 16,
        IceCubeRemoval              = 18, //layout dependent
        CircleWave                  = 19,
        TileStar                    = 20,
        Comet                       = 21,
        CometExplosion              = 22,
        CometStart                  = 23,

        /// <summary>
        /// Main fx type for regular goals with simple sprite icon and trail.
        /// The sprite is replaced at runtime.
        /// </summary>
        DoFlyResource               = 24, //goal dependent !
        LightningBoltPiece          = 25,
        LightningRainPiece          = 26, //super boost dependent
        LightningHit                = 27, //super boost dependent
        Rain                        = 28, //super boost dependent
        DoFlyStar                   = 29, //super boost dependent
        Dim                         = 30, //super boost dependent
        StarExplosion               = 31, //super boost dependent
        ChainRemove                 = 32, //layout dependent
        StickerRemove               = 33, //layout dependent
        BackgroundRemove            = 34, //layout dependent
        BackgroundDoubleRemove      = 35, //layout dependent
        PinataRemoval               = 36, //layout dependent
        FrameRemoval                = 37, //layout dependent
        AnimalRelease               = 38, //layout dependent,
        StickerThirdLayerRemove     = 39,
        StickerSecondLayerRemove    = 40,
        ChainThirdLayerRemove       = 41,
        ChainSecondLayerRemove      = 42,
        LitterDestroy               = 43,
        DoubleBomb                  = 44,
        SandRemove                  = 45,
        IvyDestroy                  = 47,
        ColorCrateThirdLayerRemove  = 48,
        ColorCrateSecondLayerRemove = 49,
        ColorCrateDestroy           = 50,
        WatermelonThirdLayerRemove  = 51,
        WatermelonSecondLayerRemove = 52,
        WatermelonDestroy           = 53,
        IceCubeThirdLevelRemove     = 54,
        IceCubeSecondLayerRemove    = 55,
        BoostRevealExplosion        = 56,
        BombBombCombine             = 57,
        DiscoBallDiscoBallCombine   = 58,
        BombLinebreakerCombine      = 59,
        VaseThirdLayerRemove        = 60,
        VaseSecondLayerRemove       = 61,
        VaseDestroy                 = 62,
        MoneyBagDestroy             = 63,
        PenguinDestroy              = 64,
        EggLayerRemove              = 65,
        EggDestroy                  = 66,
        BirdDestroy                 = 67,
        BirdAppear                  = 68,
        SheepDestroy                = 69,
        BananaAppear                = 70,
        BananaDestroy               = 71,
        SkunkAttack                 = 73,
        ChickenAppear               = 74,
        ChickenDestroy              = 75,
        BeeAppear                   = 76,
        BeeDestroy                  = 77,
        Anticipation                = 78,
        DestroyByBombBombCombo      = 79,
        MoneyBagGoal                = 80,
        BoltBoosterCombine          = 81,
        MoleDestroy                 = 82,
        BoosterSpawnFromBoltCombo   = 83,
        SquidDestroy                = 84,
        SquidGoal                   = 85,
        TntDestroy                  = 86,
        ToadGoal                    = 87,
        BoltRegularTileCombine      = 88,
        CollectEventTile            = 90,
        PropellerDestroy            = 91,
        PropellerFlight             = 92,
        SmallCrossExplosion         = 93,
        PropellerComboFlight        = 94,
        BoltTileDestroy             = 95,
        ShakeOverlayEffect          = 96,
        BushTileHideLayer5          = 97,
        BushTileHideLayer4          = 98,
        BushTileHideLayer3          = 99,
        BushTileHideLayer2          = 100,
        BushTileHideLayer1          = 101,
        MagicHatGoal                = 102,
        SafeTileHideLayer5          = 103,
        SafeTileHideLayer4          = 104,
        SafeTileHideLayer3          = 105,
        SafeTileHideLayer2          = 106,
        SafeTileHideLayer1          = 107,
        FlowerPotLayerRemove        = 108,
        FlowerPotDestroy            = 109,
        PetalRemove                 = 110,
        GrassAnticipation           = 111,
        PetalAnticipation           = 112,
        BowlingPin1                 = 113,
        BowlingPin2                 = 114,
        BowlingPin3                 = 115,
        BowlingPin4                 = 116,
        BowlingPin5                 = 117,
        BowlingPin6                 = 118,
        BowlingTileDestroy          = 119,
        SodaBottleDestroy           = 120,
        SodaTileDestroy             = 121,
        IceBarLayerRemove           = 122,
        IceBarTileDestroy           = 123,
        Swap                        = 124,
        DiscoRushCollect            = 125,
        DynamiteBoxTileDestroy      = 127,
        GiantPinataTileHideLayer3   = 128,
        GiantPinataTileHideLayer2   = 129,
        GiantPinataTileHideLayer1   = 130,
        MetalBarLayerRemove         = 131,
        MetalBarTileDestroy         = 132,
        ShelfItemDestroy            = 133,
        ShelfTileDestroy            = 134,
        JellyFishTileDestroy        = 135,
        GoldenScarabEmpty           = 136,
        GoldenScarabFull            = 137,
        GoldenScarabDestroy         = 138,
        DestructibleWallTripleRemove  = 139,
        DestructibleWallDoubleRemove  = 140,
        DestructibleWallSingleRemove  = 141,
        GondolaDestroy              = 143,
        TukTukEffect                = 144,
        VerticalBooster             = 145,
        HorizontalBooster           = 146,
        BoosterTrail                = 147,
        FireWorksFlight             = 148,
        FireWorksDestroy            = 149,
        FireWorksRemoveLayer        = 150,
        FireWorksTileDestroy        = 151,     
        SlotMachineTileHideLayer4   = 152,
        SlotMachineTileHideLayer3   = 153,
        SlotMachineTileHideLayer2   = 154,
        SlotMachineTileHideLayer1   = 155,
        SlotMachineColorBombAppear   = 156,
        SlotMachineBombAppear        = 157,
        SlotMachineLineBreakerAppear = 158,
        SlotMachineColumnBreakerAppear = 159,
        SlotMachinePropellerAppear   = 160,
        WhirlpoolSecondWave          = 161,
        SuperDiscoBallSuperDiscoBallCombine = 162,
        SquidHit                  = 163,
        PropellerFlightShadow = 164,
        GiantPinataTileHideLayer4   = 165,
        FrameLayerRemoval           = 166,
        StoneThirdLayerRemove       = 167,
        StoneSecondLayerRemove      = 168,
        StoneDestroy                = 169,
    }

    public static class FxTypeExtensions
    {
        public static string ToPrefabName(this FxType fxType)
        {
            return $"{fxType}_FX";
        }

        public static ContainerType GetContainerType(this FxType fxType)
        {
            switch (fxType)
            {
                case FxType.DoFlyResource:
                case FxType.DoFlyStar:
                    return ContainerType.UnderFX;
                case FxType.ScoreText:
                case FxType.CollectEventTile:
                    return ContainerType.ScoreFx;
                case FxType.LineBreaker:
                case FxType.TripleLineBreaker:
                case FxType.DoubleLineBreaker:
                case FxType.TukTukEffect:
                case FxType.VerticalBooster:
                case FxType.HorizontalBooster:
                    return ContainerType.LineBreakers;
                case FxType.Balloon:
                case FxType.LightningBolt:
                case FxType.LightningBoltPiece:
                case FxType.WindTrail:
                case FxType.PropellerFlight:
                case FxType.PropellerComboFlight:
                case FxType.BoosterTrail:
                    return ContainerType.ParticleFx;
                case FxType.Comet:
                case FxType.CometStart:
                case FxType.StarExplosion:
                case FxType.AnimalRelease:
                    return ContainerType.OverlayFx0;
                case FxType.LightningHit:
                case FxType.Dim:
                case FxType.LightningRainPiece:
                    return ContainerType.OverlayFx1;
                case FxType.Rain:
                    return ContainerType.OverlayFx2;
                case FxType.BoltBoosterCombine:
                case FxType.BoltRegularTileCombine:
                case FxType.BombBombCombine:
                    return ContainerType.ParticleFx;
            }

            return ContainerType.FX;
        }

        public static string ToPrefabName(this CellOverlayType type)
        {
            return "prefabs/" + type.ToString();
        }
    }

    public enum CellOverlayType
    {
        None             = 0,
        StandardOverlay  = 2,
        WallOverlay      = 3,
        DespawnerOverlay = 4,
        IvyOverlay       = 5,
        TntOverlay       = 6,
        InvisibleWallOverlay = 7,
        DestructibleWallsOverlay = 8,
        WaterOverlay     = 9,
        FlagEndOverlay   = 10
    }
}
