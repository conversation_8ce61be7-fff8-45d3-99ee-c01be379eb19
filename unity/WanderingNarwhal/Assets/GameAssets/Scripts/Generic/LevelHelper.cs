using System;
using System.Collections;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using FBConfig;
using PBGame;

namespace BBB.UI.Level
{
    public static class LevelHelper
    {
        public static Stage? PlayOnlyStageOriginal = null;
        public static Stage? PlayOnlyStageOverride = null;
        public static Stage? PlayOnlyStage => PlayOnlyStageOverride ?? PlayOnlyStageOriginal;

        public const string LEVELS_DOT_EXT = ".M3L.bytes";
        public const string LEVELS_EXT = ".m3l";

        public static List<TileKinds> TileKindsFromInt(int tileKindsInInt)
        {
            var result = new List<TileKinds>();

            var bitArray = new BitArray(BitConverter.GetBytes(tileKindsInInt));

            for (int i = 0; i < bitArray.Length; i++)
            {
                if (bitArray[i])
                    result.Add((TileKinds) (i + 1));
            }

            return result;
        }

        public static int TileKindsToInt(List<TileKinds> tileKinds)
        {
            var result = 0;
            foreach (var tileKind in tileKinds)
            {
                int value = (int) tileKind - 1;
                result += (int) Math.Pow(2, value);
            }

            return result;
        }

        public const string LevelString = "START_LEVEL_TITLE";
        public static bool ReachedFinalStage(this LevelState levelState)
        {
            var stageCount = GameAssets.Scripts.Player.Level.GetStageCount(levelState.SourceUid);
            return levelState.Stage >= stageCount;
        }

        public static float GetLevelSortOrder(IConfig config, string levelUid)
        {
            //return max value if sort order is 0f to make all new and not defined level configs be sorted as the last ones
            return config.Get<ProgressionLevelConfig>().TryGetValue(levelUid, out var levelConfig)
                ? levelConfig.SortOrder
                : float.MaxValue;
        }
    }
}